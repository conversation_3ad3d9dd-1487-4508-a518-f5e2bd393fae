'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/card'
import { Button } from '@/components/button'
import { Badge } from '@/components/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/table'
import { formatDate } from '@/lib/utils'
import { 
  Users, 
  User, 
  Phone, 
  Mail, 
  Calendar, 
  GraduationCap, 
  BookOpen,
  MapPin,
  Clock,
  CheckCircle,
  XCircle,
  Edit,
  ArrowLeft,
  UserPlus
} from 'lucide-react'
import Link from 'next/link'

interface GroupDetail {
  id: string
  name: string
  capacity: number
  schedule: string
  room: string | null
  branch: string
  startDate: string
  endDate: string
  isActive: boolean
  createdAt: string
  course: {
    id: string
    name: string
    level: string
    description: string | null
    duration: number
    price: number
  }
  teacher: {
    user: {
      id: string
      name: string
      phone: string
      email: string | null
    }
  }
  enrollments: Array<{
    id: string
    status: string
    startDate: string
    endDate: string | null
    student: {
      id: string
      name: string
      phone: string
      email: string | null
      level?: string
      branch?: string
    }
  }>
  _count: {
    enrollments: number
  }
  totalEnrolledStudents?: number
}

export default function GroupDetailPage() {
  const params = useParams()
  const [group, setGroup] = useState<GroupDetail | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (params.id) {
      fetchGroup(params.id as string)
    }
  }, [params.id])

  const fetchGroup = async (id: string) => {
    try {
      const response = await fetch(`/api/groups/${id}`)
      const data = await response.json()
      setGroup(data)
    } catch (error) {
      console.error('Error fetching group:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800'
      case 'COMPLETED':
        return 'bg-blue-100 text-blue-800'
      case 'DROPPED':
        return 'bg-red-100 text-red-800'
      case 'SUSPENDED':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount / 12500) // Convert UZS to USD for display
  }

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>
  }

  if (!group) {
    return <div className="flex justify-center items-center h-64">Group not found</div>
  }

  // Calculate statistics
  const activeEnrollments = group.enrollments.filter(e => e.status === 'ACTIVE').length
  const completedEnrollments = group.enrollments.filter(e => e.status === 'COMPLETED').length
  const droppedEnrollments = group.enrollments.filter(e => e.status === 'DROPPED').length
  const capacityUsage = (activeEnrollments / group.capacity) * 100

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/groups">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Groups
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{group.name}</h1>
            <p className="text-gray-600">Group Details & Management</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <UserPlus className="h-4 w-4 mr-2" />
            Add Student
          </Button>
          <Button>
            <Edit className="h-4 w-4 mr-2" />
            Edit Group
          </Button>
        </div>
      </div>

      {/* Group Info Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Group Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <GraduationCap className="h-5 w-5 mr-2" />
              Group Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Status</span>
              <Badge className={group.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                {group.isActive ? 'Active' : 'Inactive'}
              </Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Capacity</span>
              <span className="font-semibold">{activeEnrollments}/{group.capacity}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Branch</span>
              <span className="font-semibold">{group.branch}</span>
            </div>
            {group.room && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Room</span>
                <span className="font-semibold">{group.room}</span>
              </div>
            )}
            <div className="pt-2 border-t">
              <div className="flex items-center space-x-2 mb-2">
                <Calendar className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-600">Duration</span>
              </div>
              <p className="text-sm">{formatDate(group.startDate)} - {formatDate(group.endDate)}</p>
            </div>
          </CardContent>
        </Card>

        {/* Course Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BookOpen className="h-5 w-5 mr-2" />
              Course Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold text-lg">{group.course.name}</h3>
              <p className="text-sm text-gray-600">Level: {group.course.level}</p>
            </div>
            {group.course.description && (
              <p className="text-sm text-gray-700">{group.course.description}</p>
            )}
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Duration</span>
              <span className="font-semibold">{group.course.duration} weeks</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Price</span>
              <span className="font-semibold">{formatCurrency(group.course.price)}</span>
            </div>
          </CardContent>
        </Card>

        {/* Teacher Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              Teacher
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold text-lg">{group.teacher.user.name}</h3>
            </div>
            <div className="flex items-center space-x-3">
              <Phone className="h-4 w-4 text-gray-400" />
              <span className="text-sm">{group.teacher.user.phone}</span>
            </div>
            {group.teacher.user.email && (
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-gray-400" />
                <span className="text-sm">{group.teacher.user.email}</span>
              </div>
            )}

          </CardContent>
        </Card>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Students</p>
                <p className="text-2xl font-bold text-gray-900">{activeEnrollments}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-gray-900">{completedEnrollments}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <XCircle className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Dropped</p>
                <p className="text-2xl font-bold text-gray-900">{droppedEnrollments}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Capacity Usage</p>
                <p className="text-2xl font-bold text-gray-900">{capacityUsage.toFixed(0)}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enrolled Students */}
      <Card>
        <CardHeader>
          <CardTitle>Enrolled Students ({group.enrollments.length})</CardTitle>
          <CardDescription>Students currently enrolled in this group</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Enrollment Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {group.enrollments.map((enrollment) => (
                <TableRow key={enrollment.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                          <User className="h-4 w-4 text-gray-600" />
                        </div>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {enrollment.student.name}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="text-sm">{enrollment.student.phone}</div>
                      {enrollment.student.email && (
                        <div className="text-sm text-gray-500">{enrollment.student.email}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{formatDate(enrollment.startDate)}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(enrollment.status)}>
                      {enrollment.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        View
                      </Button>
                      <Button variant="outline" size="sm">
                        Edit
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>


    </div>
  )
}

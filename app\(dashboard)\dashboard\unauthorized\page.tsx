"use client"

import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/card"
import { Alert<PERSON><PERSON>gle, ArrowLeft, Home, Shield } from "lucide-react"
import Link from "next/link"

export default function UnauthorizedPage() {
  const router = useRouter()
  const { data: session } = useSession()

  const handleGoBack = () => {
    router.back()
  }

  const getDashboardUrl = () => {
    if (!session?.user) return "/dashboard"
    
    const userRole = (session.user as any).role
    
    switch (userRole) {
      case "STUDENT":
        return "/dashboard/student"
      case "TEACHER":
        return "/dashboard/teacher"
      case "RECEPTION":
        return "/dashboard/leads"
      case "CASHIER":
        return "/dashboard/payments"
      case "ACADEMIC_MANAGER":
        return "/dashboard/assessments"
      default:
        return "/dashboard"
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center px-4">
      <div className="w-full max-w-md">
        <Card className="border-red-200">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
            <CardTitle className="text-2xl text-red-800">Access Denied</CardTitle>
            <CardDescription className="text-red-600">
              You don&apos;t have permission to access this page
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-center">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                <Shield className="h-6 w-6 text-red-600 mx-auto mb-2" />
                <p className="text-sm text-red-700">
                  This page requires specific permissions that your account doesn&apos;t have.
                </p>
              </div>
              
              {session?.user && (
                <div className="text-sm text-gray-600 mb-4">
                  <p>Logged in as: <span className="font-medium">{(session.user as any).name}</span></p>
                  <p>Role: <span className="font-medium capitalize">{(session.user as any).role?.toLowerCase()}</span></p>
                </div>
              )}
            </div>

            <div className="space-y-3">
              <Button 
                onClick={handleGoBack} 
                variant="outline" 
                className="w-full"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Go Back
              </Button>
              
              <Link href={getDashboardUrl()} className="block">
                <Button className="w-full">
                  <Home className="h-4 w-4 mr-2" />
                  Go to Dashboard
                </Button>
              </Link>
            </div>

            <div className="text-center">
              <p className="text-sm text-gray-500">
                Need access to this page?{" "}
                <Link 
                  href="/contact" 
                  className="text-blue-600 hover:text-blue-500 font-medium"
                >
                  Contact Administrator
                </Link>
              </p>
            </div>

            {/* Common Access Levels */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Access Levels:</h4>
              <div className="text-xs text-gray-600 space-y-1">
                <div><strong>Admin:</strong> Full system access</div>
                <div><strong>Manager:</strong> Operations and reporting</div>
                <div><strong>Teacher:</strong> Classes and students</div>
                <div><strong>Reception:</strong> Leads and enrollments</div>
                <div><strong>Cashier:</strong> Payments and billing</div>
                <div><strong>Student/Parent:</strong> Personal dashboard</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

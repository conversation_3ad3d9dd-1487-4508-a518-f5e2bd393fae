'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/button'
import { Input } from '@/components/input'
import { Label } from '@/components/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/select'
import { Alert, AlertDescription } from '@/components/alert'
import { Badge } from '@/components/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/card'
import { Search, Users, BookOpen, User, Loader2, Plus, X } from 'lucide-react'
import { useBranch } from '@/contexts/branch-context'

interface Group {
  id: string
  name: string
  course: {
    name: string
    level: string
  }
  teacher: {
    user: {
      name: string
    }
  }
  _count: {
    enrollments: number
  }
}

interface AssignGroupToCabinetFormProps {
  cabinetId: string
  cabinetName: string
  onSuccess: () => void
  onCancel: () => void
}

export default function AssignGroupToCabinetForm({
  cabinetId,
  cabinetName,
  onSuccess,
  onCancel
}: AssignGroupToCabinetFormProps) {
  const { currentBranch } = useBranch()
  const [groups, setGroups] = useState<Group[]>([])
  const [loading, setLoading] = useState(true)
  const [assigning, setAssigning] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedGroupId, setSelectedGroupId] = useState<string>('')

  useEffect(() => {
    fetchUnassignedGroups()
  }, [currentBranch.id, searchTerm])

  const fetchUnassignedGroups = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        branch: currentBranch.id,
        ...(searchTerm && { search: searchTerm }),
      })

      const response = await fetch(`/api/cabinets/unassigned-groups?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch unassigned groups')
      }

      const data = await response.json()
      setGroups(data.groups || [])
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleAssignGroup = async () => {
    if (!selectedGroupId) {
      setError('Please select a group to assign')
      return
    }

    try {
      setAssigning(true)
      setError(null)

      const response = await fetch(`/api/cabinets/${cabinetId}/assign-group`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ groupId: selectedGroupId }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to assign group')
      }

      onSuccess()
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setAssigning(false)
    }
  }

  const filteredGroups = groups.filter(group =>
    group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.teacher.user.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Assign Group to {cabinetName}</h3>
        <p className="text-sm text-gray-500">
          Select an unassigned group to assign to this cabinet
        </p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Search */}
      <div className="space-y-2">
        <Label htmlFor="search">Search Groups</Label>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            id="search"
            placeholder="Search by group name, course, or teacher..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Group Selection */}
      <div className="space-y-2">
        <Label>Available Groups ({filteredGroups.length})</Label>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading groups...</span>
          </div>
        ) : filteredGroups.length === 0 ? (
          <Card>
            <CardContent className="py-8 text-center">
              <p className="text-gray-500">
                {searchTerm ? 'No groups found matching your search.' : 'No unassigned groups available.'}
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-3 max-h-96 overflow-y-auto">
            {filteredGroups.map((group) => (
              <Card
                key={group.id}
                className={`cursor-pointer transition-all ${
                  selectedGroupId === group.id
                    ? 'ring-2 ring-blue-500 bg-blue-50'
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => setSelectedGroupId(group.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium">{group.name}</h4>
                        <Badge variant="outline">
                          {group.course.level}
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                          <BookOpen className="h-3 w-3" />
                          <span>{group.course.name}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <User className="h-3 w-3" />
                          <span>{group.teacher.user.name}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Users className="h-3 w-3" />
                          <span>{group._count.enrollments} students</span>
                        </div>
                      </div>
                    </div>
                    {selectedGroupId === group.id && (
                      <div className="text-blue-600">
                        <Plus className="h-5 w-5" />
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="flex justify-end space-x-3">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          onClick={handleAssignGroup}
          disabled={!selectedGroupId || assigning}
        >
          {assigning ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Assigning...
            </>
          ) : (
            <>
              <Plus className="h-4 w-4 mr-2" />
              Assign Group
            </>
          )}
        </Button>
      </div>
    </div>
  )
}

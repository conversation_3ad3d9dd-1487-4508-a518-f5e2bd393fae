"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/button"
import { Input } from "@/components/input"
import { Badge } from "@/components/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/table"
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/dialog"
import { PaymentForm } from "@/components/forms/payment-form"
import { Search, Plus, Edit, Trash2, Download, Filter, Calendar } from "lucide-react"

interface Payment {
  id: string
  studentId: string
  student: {
    id: string
    name: string
    phone: string
    email?: string
    level?: string
    branch?: string
  }
  amount: number
  method: string
  status: string
  description: string | null
  transactionId: string | null
  dueDate: string | null
  paidDate: string | null
  createdAt: string
  updatedAt: string
}

interface PaymentsTableProps {
  initialData?: Payment[]
}

export function PaymentsTable({ initialData = [] }: PaymentsTableProps) {
  const [payments, setPayments] = useState<Payment[]>(initialData)
  const [filteredPayments, setFilteredPayments] = useState<Payment[]>(initialData)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("")
  const [methodFilter, setMethodFilter] = useState("")
  const [dateFilter, setDateFilter] = useState("")
  const [loading, setLoading] = useState(false)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingPayment, setEditingPayment] = useState<Payment | null>(null)

  // Fetch payments data
  const fetchPayments = async () => {
    setLoading(true)
    try {
      const response = await fetch("/api/payments")
      if (response.ok) {
        const data = await response.json()
        setPayments(data)
        setFilteredPayments(data)
      }
    } catch (error) {
      console.error("Error fetching payments:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (initialData.length === 0) {
      fetchPayments()
    }
  }, [initialData])

  // Filter payments based on search and filters
  useEffect(() => {
    let filtered = payments

    if (searchTerm) {
      filtered = filtered.filter(
        (payment) =>
          payment.student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          payment.student.phone.includes(searchTerm) ||
          payment.transactionId?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          payment.description?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter) {
      filtered = filtered.filter((payment) => payment.status === statusFilter)
    }

    if (methodFilter) {
      filtered = filtered.filter((payment) => payment.method === methodFilter)
    }

    if (dateFilter) {
      const filterDate = new Date(dateFilter)
      filtered = filtered.filter((payment) => {
        const paymentDate = new Date(payment.createdAt)
        return paymentDate.toDateString() === filterDate.toDateString()
      })
    }

    setFilteredPayments(filtered)
  }, [payments, searchTerm, statusFilter, methodFilter, dateFilter])

  // Get unique statuses and methods for filters
  const uniqueStatuses = [...new Set(payments.map((payment) => payment.status))]
  const uniqueMethods = [...new Set(payments.map((payment) => payment.method))]

  // Handle payment deletion
  const handleDelete = async (paymentId: string) => {
    if (!confirm("Are you sure you want to delete this payment?")) return

    try {
      const response = await fetch(`/api/payments/${paymentId}`, {
        method: "DELETE",
      })

      if (response.ok) {
        setPayments(payments.filter((payment) => payment.id !== paymentId))
      } else {
        alert("Failed to delete payment")
      }
    } catch (error) {
      console.error("Error deleting payment:", error)
      alert("Error deleting payment")
    }
  }

  // Handle form submission
  const handleFormSubmit = async (data: any) => {
    try {
      // Submit the payment data to the API
      const response = await fetch('/api/payments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        throw new Error('Failed to save payment')
      }

      setIsCreateDialogOpen(false)
      setEditingPayment(null)
      fetchPayments()
    } catch (error) {
      console.error('Error saving payment:', error)
      throw error
    }
  }

  // Export to CSV
  const exportToCSV = () => {
    const headers = [
      "Student Name",
      "Phone",
      "Amount",
      "Method",
      "Status",
      "Description",
      "Transaction ID",
      "Due Date",
      "Paid Date",
      "Created",
    ]
    const csvData = filteredPayments.map((payment) => [
      payment.student.name,
      payment.student.phone,
      payment.amount,
      payment.method,
      payment.status,
      payment.description || "",
      payment.transactionId || "",
      payment.dueDate ? new Date(payment.dueDate).toLocaleDateString() : "",
      payment.paidDate ? new Date(payment.paidDate).toLocaleDateString() : "",
      new Date(payment.createdAt).toLocaleDateString(),
    ])

    const csvContent = [headers, ...csvData]
      .map((row) => row.map((field) => `"${field}"`).join(","))
      .join("\n")

    const blob = new Blob([csvContent], { type: "text/csv" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `payments-${new Date().toISOString().split("T")[0]}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "PAID":
        return "default"
      case "DEBT":
        return "destructive"
      case "REFUNDED":
        return "outline"
      default:
        return "secondary"
    }
  }

  // Calculate totals
  const totalAmount = filteredPayments.reduce((sum, payment) => sum + payment.amount, 0)
  const paidAmount = filteredPayments
    .filter((payment) => payment.status === "PAID")
    .reduce((sum, payment) => sum + payment.amount, 0)
  const debtAmount = filteredPayments
    .filter((payment) => payment.status === "DEBT")
    .reduce((sum, payment) => sum + payment.amount, 0)

  return (
    <div className="space-y-4">
      {/* Header with actions */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <h2 className="text-2xl font-bold">Payments</h2>
        <div className="flex gap-2">
          <Button onClick={exportToCSV} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Payment
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Add New Payment</DialogTitle>
              </DialogHeader>
              <PaymentForm onSubmit={handleFormSubmit} />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-blue-600">Total Amount</h3>
          <p className="text-2xl font-bold text-blue-900">
            {totalAmount.toLocaleString()} UZS
          </p>
        </div>
        <div className="bg-green-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-green-600">Paid Amount</h3>
          <p className="text-2xl font-bold text-green-900">
            {paidAmount.toLocaleString()} UZS
          </p>
        </div>
        <div className="bg-yellow-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-yellow-600">Pending Amount</h3>
          <p className="text-2xl font-bold text-yellow-900">
            {debtAmount.toLocaleString()} UZS
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search payments..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-3 py-2 border rounded-md"
        >
          <option value="">All Statuses</option>
          {uniqueStatuses.map((status) => (
            <option key={status} value={status}>
              {status}
            </option>
          ))}
        </select>
        <select
          value={methodFilter}
          onChange={(e) => setMethodFilter(e.target.value)}
          className="px-3 py-2 border rounded-md"
        >
          <option value="">All Methods</option>
          {uniqueMethods.map((method) => (
            <option key={method} value={method}>
              {method}
            </option>
          ))}
        </select>
        <Input
          type="date"
          value={dateFilter}
          onChange={(e) => setDateFilter(e.target.value)}
          className="w-auto"
        />
        {(statusFilter || methodFilter || dateFilter || searchTerm) && (
          <Button
            variant="outline"
            onClick={() => {
              setStatusFilter("")
              setMethodFilter("")
              setDateFilter("")
              setSearchTerm("")
            }}
          >
            Clear Filters
          </Button>
        )}
      </div>

      {/* Table */}
      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Student</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Method</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Due Date</TableHead>
              <TableHead>Paid Date</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  Loading payments...
                </TableCell>
              </TableRow>
            ) : filteredPayments.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  No payments found
                </TableCell>
              </TableRow>
            ) : (
              filteredPayments.map((payment) => (
                <TableRow key={payment.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{payment.student.name}</div>
                      <div className="text-sm text-gray-500">
                        {payment.student.phone}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="font-medium">
                    {payment.amount.toLocaleString()} UZS
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{payment.method}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(payment.status)}>
                      {payment.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {payment.description || "N/A"}
                  </TableCell>
                  <TableCell>
                    {payment.dueDate
                      ? new Date(payment.dueDate).toLocaleDateString()
                      : "N/A"}
                  </TableCell>
                  <TableCell>
                    {payment.paidDate
                      ? new Date(payment.paidDate).toLocaleDateString()
                      : "N/A"}
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setEditingPayment(payment)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>Edit Payment</DialogTitle>
                          </DialogHeader>
                          {editingPayment && (
                            <PaymentForm
                              initialData={{
                                studentId: editingPayment.studentId,
                                amount: editingPayment.amount,
                                method: editingPayment.method as any,
                                status: editingPayment.status as any,
                                description: editingPayment.description || undefined,
                                learningStartDate: '',
                                learningEndDate: '',
                              }}
                              onSubmit={handleFormSubmit}
                              isEditing={true}
                            />
                          )}
                        </DialogContent>
                      </Dialog>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(payment.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Results count */}
      <div className="text-sm text-gray-500">
        Showing {filteredPayments.length} of {payments.length} payments
      </div>
    </div>
  )
}
